/**
 * ProductCompareBasic 基础产品对比测试脚本
 * 测试非AI版本的产品对比功能 - 直接返回原始产品参数数据
 * 功能: 从productCompareBasic获取原始产品参数数据并保存到文件
 * 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro
 * 重点: 获取最原始的产品参数数据，不进行AI分析
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { compareProductsByNamesBasic } = require('../src/services/product/productCompareBasic');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 测试基础产品对比功能 - 保存原始数据
 */
async function testBasicProductComparison() {
  console.log('\n🆚 测试: 基础产品对比功能 (华为 Mate 70 Pro vs 苹果iPhone 16 Pro)');
  console.log('='.repeat(80));

  const productNames = [
    '华为 Mate 70 Pro',       // 使用数据库中实际存在的产品名称
    '苹果iPhone 16 Pro'       // 使用数据库中实际存在的产品名称
  ];

  console.log(`📱 对比产品: ${productNames.join(' vs ')}`);
  console.log(`💡 注意: 基础版本测试，仅获取和保存原始产品参数数据，无AI分析`);

  try {
    const startTime = Date.now();

    const result = await compareProductsByNamesBasic(productNames);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️ 对比耗时: ${duration}ms`);

    if (result.success) {
      console.log('\n✅ 基础产品对比成功!');
      console.log(`📊 找到产品数量: ${result.data.products.length}`);
      console.log(`📋 产品类别: ${result.data.basicStats.productCategory}`);
      console.log(`🔢 参数分类数量: ${result.data.basicStats.totalProducts}`);
      console.log(`📝 未找到产品数量: ${result.data.basicStats.notFoundProducts}`);

      // 保存原始数据到文件
      try {
        const basicFileName = 'productCompareBasic_result.json';
        const basicFilePath = path.join(__dirname, basicFileName);

        // 保存完整的原始数据
        fs.writeFileSync(basicFilePath, JSON.stringify(result, null, 2), 'utf8');
        console.log(`\n📄 基础对比原始数据已保存到文件: ${basicFilePath}`);
        console.log(`📂 文件大小: ${fs.statSync(basicFilePath).size} 字节`);

        // 单独保存原始产品数据
        const rawProductDataFileName = 'rawProductData_basic.json';
        const rawProductDataFilePath = path.join(__dirname, rawProductDataFileName);

        fs.writeFileSync(rawProductDataFilePath, JSON.stringify(result.data.rawProductData, null, 2), 'utf8');
        console.log(`📄 原始产品数据已保存到文件: ${rawProductDataFilePath}`);

        // 单独保存原始对比数据
        const rawComparisonDataFileName = 'rawComparisonData_basic.json';
        const rawComparisonDataFilePath = path.join(__dirname, rawComparisonDataFileName);

        fs.writeFileSync(rawComparisonDataFilePath, JSON.stringify(result.data.rawComparisonData, null, 2), 'utf8');
        console.log(`📄 原始对比数据已保存到文件: ${rawComparisonDataFilePath}`);

        console.log('\n✅ 所有原始数据保存完成，无AI处理，保持数据最原始状态');

      } catch (saveError) {
        console.error(`❌ 保存文件失败: ${saveError.message}`);
      }

    } else {
      console.log(`❌ 基础产品对比失败: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ 基础产品对比失败: ${error.message}`);
    console.error('详细错误:', error.stack);
  }
}

/**
 * 测试多产品对比
 */
async function testMultipleProductComparison() {
  console.log('\n🆚 测试: 多产品对比功能');
  console.log('='.repeat(80));

  const productNames = [
    '华为 Mate 70 Pro',
    '苹果iPhone 16 Pro',
    '小米15 Pro'  // 如果数据库中有的话
  ];

  console.log(`📱 对比产品: ${productNames.join(' vs ')}`);

  try {
    const result = await compareProductsByNamesBasic(productNames);

    if (result.success) {
      console.log('\n✅ 多产品对比成功!');
      console.log(`📊 找到产品数量: ${result.data.products.length}`);
      console.log(`⚠️ 未找到产品: ${result.data.notFoundProducts.join(', ') || '无'}`);
      
      // 保存多产品对比结果
      const multiFileName = 'multiProductCompare_basic.json';
      const multiFilePath = path.join(__dirname, multiFileName);
      
      fs.writeFileSync(multiFilePath, JSON.stringify(result, null, 2), 'utf8');
      console.log(`📄 多产品对比数据已保存到文件: ${multiFilePath}`);

    } else {
      console.log(`❌ 多产品对比失败: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ 多产品对比失败: ${error.message}`);
  }
}

/**
 * 测试错误处理功能
 */
async function testErrorHandling() {
  console.log('\n🚨 测试: 错误处理功能');
  console.log('='.repeat(80));

  // 测试1: 产品数量不足
  console.log('\n测试1: 产品数量不足');
  try {
    const result = await compareProductsByNamesBasic(['华为 Mate 70 Pro']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }

  // 测试2: 产品数量过多
  console.log('\n测试2: 产品数量过多');
  try {
    const tooManyProducts = Array(8).fill('华为 Mate 70 Pro');
    const result = await compareProductsByNamesBasic(tooManyProducts);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }

  // 测试3: 不存在的产品
  console.log('\n测试3: 不存在的产品');
  try {
    const result = await compareProductsByNamesBasic(['不存在的产品A', '不存在的产品B']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }

  // 测试4: 空数组
  console.log('\n测试4: 空数组');
  try {
    const result = await compareProductsByNamesBasic([]);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runAllBasicTests() {
  console.log('🚀 ProductCompareBasic 基础对比测试开始');
  console.log('='.repeat(80));
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`测试重点: 获取和保存最原始的产品参数数据，无AI分析`);

  try {
    // 连接数据库
    await connectDB();

    // 执行基础产品对比测试
    await testBasicProductComparison();

    // 执行多产品对比测试
    await testMultipleProductComparison();

    // 执行错误处理测试
    await testErrorHandling();

    console.log('\n✅ 所有基础测试完成!');
    console.log('\n📊 测试总结:');
    console.log('   - 基础功能测试: 非AI产品对比功能');
    console.log('   - 原始数据保存: 保存完整的原始产品参数');
    console.log('   - 多产品测试: 支持多个产品同时对比');
    console.log('   - 错误处理测试: 各种异常情况处理');
    console.log('   - 数据纯净性: 无AI处理，保持数据原始状态');

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    console.error('详细错误:', error.stack);
  } finally {
    // 断开数据库连接
    await disconnectDB();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 检查环境变量
  console.log('🔧 环境检查:');
  console.log(`   MongoDB URI: ${DB_URL}`);
  console.log(`   测试类型: 基础对比（非AI版本）`);

  runAllBasicTests().catch(error => {
    console.error('测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testBasicProductComparison,
  testMultipleProductComparison,
  testErrorHandling,
  runAllBasicTests
};
