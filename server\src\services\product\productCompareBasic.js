const { findProductsByNamesV4, formatProductForAI } = require('./productCompare');

/**
 * 产品基础对比服务 - 非AI版本
 * 功能：
 * 1. 复用现有的产品查询和格式化逻辑
 * 2. 直接返回产品的原始参数，不进行AI分析
 * 3. 不包含缓存功能
 * 4. 提供结构化的产品参数数据
 */

/**
 * 提取和整理产品参数信息
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 整理后的参数信息
 */
function organizeProductParameters(formattedProducts) {
  console.log('📊 开始整理产品参数信息...');
  
  // 1. 分析产品类型
  const productTypes = [...new Set(formattedProducts.map(p => p.productType))];
  const primaryProductType = productTypes[0];
  
  console.log(`📋 产品类型: ${productTypes.join(', ')}`);
  
  // 2. 收集所有参数分类
  const allCategories = new Set();
  const parametersByCategory = {};
  
  // 3. 遍历所有产品，收集参数分类
  formattedProducts.forEach((product, index) => {
    console.log(`📊 分析产品 ${index + 1}: ${product.name}`);
    
    // 分析 commonSpecs
    if (product.commonSpecs) {
      Object.keys(product.commonSpecs).forEach(category => {
        allCategories.add(category);
        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Set();
        }
        
        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            parametersByCategory[category].add(param);
          });
        }
      });
    }
    
    // 分析 configurations 中的 specs
    if (product.configurations) {
      product.configurations.forEach(config => {
        if (config.specs) {
          Object.keys(config.specs).forEach(category => {
            allCategories.add(category);
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Set();
            }
            
            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                parametersByCategory[category].add(param);
              });
            }
          });
        }
      });
    }
  });
  
  // 4. 转换 Set 为 Array 并排序
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    finalParametersByCategory[category] = Array.from(parametersByCategory[category]).sort();
  });
  
  // 5. 按参数数量排序分类
  const sortedCategories = Object.keys(finalParametersByCategory)
    .sort((a, b) => finalParametersByCategory[b].length - finalParametersByCategory[a].length);
  
  const result = {
    productTypes: productTypes,
    primaryProductType: primaryProductType,
    totalCategories: allCategories.size,
    categories: sortedCategories,
    parametersByCategory: finalParametersByCategory,
    totalProducts: formattedProducts.length
  };
  
  console.log(`✅ 参数整理完成: 发现 ${result.totalCategories} 个参数分类`);
  
  return result;
}

/**
 * 构建产品参数对比表格数据
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {Object} parameterInfo 参数信息
 * @returns {Object} 对比表格数据
 */
function buildComparisonTable(formattedProducts, parameterInfo) {
  console.log('🔧 构建产品参数对比表格...');
  
  const comparisonTable = {};
  
  // 遍历每个参数分类
  parameterInfo.categories.forEach(category => {
    comparisonTable[category] = {};
    
    // 遍历该分类下的每个参数
    parameterInfo.parametersByCategory[category].forEach(parameter => {
      comparisonTable[category][parameter] = {};
      
      // 为每个产品收集该参数的值
      formattedProducts.forEach(product => {
        let parameterValue = null;
        
        // 从 commonSpecs 中查找
        if (product.commonSpecs && 
            product.commonSpecs[category] && 
            product.commonSpecs[category][parameter] !== undefined) {
          parameterValue = product.commonSpecs[category][parameter];
        }
        
        // 如果在 commonSpecs 中没找到，从 configurations 中查找
        if (parameterValue === null && product.configurations) {
          for (const config of product.configurations) {
            if (config.specs && 
                config.specs[category] && 
                config.specs[category][parameter] !== undefined) {
              parameterValue = config.specs[category][parameter];
              break; // 找到第一个就停止
            }
          }
        }
        
        comparisonTable[category][parameter][product.name] = parameterValue;
      });
    });
  });
  
  console.log('✅ 对比表格构建完成');
  return comparisonTable;
}

/**
 * 根据产品名称列表获取基础产品对比数据（非AI版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesBasic = async (productNames) => {
  try {
    console.log('🔍 开始基础产品对比（非AI版本）');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 复用现有的产品查找逻辑
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 整理产品参数信息
    const parameterInfo = organizeProductParameters(formattedProductData);

    // 5. 构建参数对比表格
    const comparisonTable = buildComparisonTable(formattedProductData, parameterInfo);

    // 6. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes[0];
    const isSameCategory = productTypes.length === 1;

    // 7. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 产品基本信息
        products: products.map(product => ({
          skuName: product.skuName,
          imageUrl: product.imageUrl,
          productType: product.productType
        })),

        // 产品详细参数数据
        productDetails: formattedProductData,

        // 参数分析信息
        parameterAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          totalProducts: products.length,
          totalCategories: parameterInfo.totalCategories,
          categories: parameterInfo.categories,
          parametersByCategory: parameterInfo.parametersByCategory
        },

        // 参数对比表格
        comparisonTable: comparisonTable,

        // 未找到的产品
        notFoundProducts: notFoundProducts,

        // 元数据
        metadata: {
          compareType: 'basic',
          timestamp: new Date().toISOString(),
          version: '1.0'
        }
      }
    };

    console.log('✅ 基础产品对比完成');
    return result;

  } catch (error) {
    console.error('❌ 基础产品对比失败:', error);
    return {
      success: false,
      error: `基础产品对比失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  compareProductsByNamesBasic,
  organizeProductParameters,
  buildComparisonTable
};
