const { findProductsByNamesV4, formatProductForAI } = require('./productCompare');

/**
 * 产品基础对比服务 - 非AI版本
 * 功能：
 * 1. 复用现有的产品查询和格式化逻辑
 * 2. 直接返回产品的最原始参数数据，不进行AI分析
 * 3. 不包含缓存功能
 * 4. 保持数据的原始性，不添加额外处理
 */

/**
 * 简单收集产品参数分类信息（最基础版本）
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 基础参数信息
 */
function organizeProductParameters(formattedProducts) {
  console.log('📊 收集产品参数分类信息...');

  const allCategories = new Set();
  const parametersByCategory = {};

  // 遍历所有产品，收集参数分类
  formattedProducts.forEach(product => {
    // 收集 commonSpecs 分类
    if (product.commonSpecs) {
      Object.keys(product.commonSpecs).forEach(category => {
        allCategories.add(category);
        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Set();
        }

        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            parametersByCategory[category].add(param);
          });
        }
      });
    }

    // 收集 configurations 中的 specs 分类
    if (product.configurations) {
      product.configurations.forEach(config => {
        if (config.specs) {
          Object.keys(config.specs).forEach(category => {
            allCategories.add(category);
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Set();
            }

            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                parametersByCategory[category].add(param);
              });
            }
          });
        }
      });
    }
  });

  // 转换为数组格式
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    finalParametersByCategory[category] = Array.from(parametersByCategory[category]).sort();
  });

  const sortedCategories = Object.keys(finalParametersByCategory).sort();

  console.log(`✅ 收集完成: ${allCategories.size} 个参数分类`);

  return {
    totalCategories: allCategories.size,
    categories: sortedCategories,
    parametersByCategory: finalParametersByCategory
  };
}

/**
 * 构建简单的产品参数对比数据
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {Object} parameterInfo 参数信息
 * @returns {Object} 简单对比数据
 */
function buildComparisonTable(formattedProducts, parameterInfo) {
  console.log('🔧 构建参数对比数据...');

  const comparisonData = {};

  // 为每个分类构建对比数据
  parameterInfo.categories.forEach(category => {
    comparisonData[category] = {};

    // 为每个参数收集所有产品的值
    parameterInfo.parametersByCategory[category].forEach(parameter => {
      comparisonData[category][parameter] = {};

      formattedProducts.forEach(product => {
        let value = null;

        // 从 commonSpecs 获取值
        if (product.commonSpecs?.[category]?.[parameter] !== undefined) {
          value = product.commonSpecs[category][parameter];
        }

        // 从 configurations 获取值（如果 commonSpecs 中没有）
        if (value === null && product.configurations) {
          for (const config of product.configurations) {
            if (config.specs?.[category]?.[parameter] !== undefined) {
              value = config.specs[category][parameter];
              break;
            }
          }
        }

        comparisonData[category][parameter][product.name] = value;
      });
    });
  });

  console.log('✅ 对比数据构建完成');
  return comparisonData;
}

/**
 * 根据产品名称列表获取基础产品对比数据（非AI版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesBasic = async (productNames) => {
  try {
    console.log('🔍 开始基础产品对比（非AI版本）');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 复用现有的产品查找逻辑
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 整理产品参数信息
    const parameterInfo = organizeProductParameters(formattedProductData);

    // 5. 构建参数对比表格
    const comparisonTable = buildComparisonTable(formattedProductData, parameterInfo);

    // 6. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes[0];
    const isSameCategory = productTypes.length === 1;

    // 7. 构建最终返回结果 - 保持最原始的数据结构
    const result = {
      success: true,
      data: {
        // 产品基本信息（最简化）
        products: products.map(product => ({
          skuName: product.skuName,
          imageUrl: product.imageUrl,
          productType: product.productType
        })),

        // 原始产品数据（完全未处理的格式化数据）
        rawProductData: formattedProductData,

        // 基础参数统计（最简化）
        basicStats: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          totalProducts: products.length,
          foundProducts: products.length,
          notFoundProducts: notFoundProducts.length
        },

        // 原始参数对比数据
        rawComparisonData: comparisonTable,

        // 未找到的产品列表
        notFoundProducts: notFoundProducts
      }
    };

    console.log('✅ 基础产品对比完成');
    return result;

  } catch (error) {
    console.error('❌ 基础产品对比失败:', error);
    return {
      success: false,
      error: `基础产品对比失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  compareProductsByNamesBasic,
  organizeProductParameters,
  buildComparisonTable
};
