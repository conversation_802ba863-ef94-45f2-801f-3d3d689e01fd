/**
 * 基础产品对比功能测试示例
 * 
 * 这个文件展示了如何使用 productCompareBasic.js 中的功能
 * 注意：这不是完整的单元测试，而是使用示例
 */

const { compareProductsByNamesBasic } = require('./productCompareBasic');

/**
 * 测试基础产品对比功能
 */
async function testBasicProductComparison() {
  try {
    console.log('🧪 开始测试基础产品对比功能...');
    
    // 示例产品名称列表（请根据实际数据库中的产品名称修改）
    const testProductNames = [
      'iPhone 15 Pro',
      'iPhone 15',
      'Samsung Galaxy S24'
    ];
    
    console.log('测试产品列表:', testProductNames);
    
    // 调用基础对比功能
    const result = await compareProductsByNamesBasic(testProductNames);
    
    if (result.success) {
      console.log('✅ 对比成功！');
      console.log('找到的产品数量:', result.data.products.length);
      console.log('产品类别:', result.data.parameterAnalysis.productCategory);
      console.log('参数分类数量:', result.data.parameterAnalysis.totalCategories);
      console.log('参数分类列表:', result.data.parameterAnalysis.categories);
      
      // 显示部分对比数据示例
      console.log('\n📊 对比表格示例（前3个分类）:');
      const categories = result.data.parameterAnalysis.categories.slice(0, 3);
      categories.forEach(category => {
        console.log(`\n分类: ${category}`);
        const categoryData = result.data.comparisonTable[category];
        Object.keys(categoryData).slice(0, 3).forEach(parameter => {
          console.log(`  参数: ${parameter}`);
          Object.keys(categoryData[parameter]).forEach(productName => {
            const value = categoryData[parameter][productName];
            console.log(`    ${productName}: ${value || '无数据'}`);
          });
        });
      });
      
    } else {
      console.log('❌ 对比失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

/**
 * 测试错误情况
 */
async function testErrorCases() {
  console.log('\n🧪 测试错误情况...');
  
  // 测试产品数量不足
  console.log('测试1: 产品数量不足');
  const result1 = await compareProductsByNamesBasic(['iPhone 15']);
  console.log('结果:', result1.success ? '成功' : result1.error);
  
  // 测试产品数量过多
  console.log('\n测试2: 产品数量过多');
  const tooManyProducts = Array(8).fill('iPhone 15');
  const result2 = await compareProductsByNamesBasic(tooManyProducts);
  console.log('结果:', result2.success ? '成功' : result2.error);
  
  // 测试空数组
  console.log('\n测试3: 空数组');
  const result3 = await compareProductsByNamesBasic([]);
  console.log('结果:', result3.success ? '成功' : result3.error);
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行基础产品对比测试...\n');
  
  await testBasicProductComparison();
  await testErrorCases();
  
  console.log('\n✅ 所有测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testBasicProductComparison,
  testErrorCases,
  runAllTests
};
